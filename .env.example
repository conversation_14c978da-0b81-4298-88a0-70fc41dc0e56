# 智阅AI环境配置示例文件
# 复制此文件为 .env 并填入实际值

# 基础配置
DEBUG=true
APP_NAME="智阅AI"
VERSION="1.0.0"

# 数据库配置
# 开发环境使用SQLite
DATABASE_URL="sqlite:///./zhiyue_ai.db"
# 生产环境使用PostgreSQL
# DATABASE_URL="postgresql://username:password@localhost:5432/zhiyue_ai"

# Redis配置
REDIS_URL="redis://localhost:6379/0"
CELERY_BROKER_URL="redis://localhost:6379/1"
CELERY_RESULT_BACKEND="redis://localhost:6379/2"

# 安全配置
SECRET_KEY="your-secret-key-here-change-in-production"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件存储配置
STORAGE_BASE_PATH="./storage"
MAX_FILE_SIZE=52428800  # 50MB in bytes
ALLOWED_FILE_EXTENSIONS=".pdf,.jpg,.jpeg,.png,.tiff,.tif"

# Gemini 2.5 Pro OCR配置
GEMINI_API_KEY="your-gemini-api-key-here"
GEMINI_MODEL="gemini-2.5-pro"
GEMINI_BASE_URL="https://generativelanguage.googleapis.com/v1beta"
GEMINI_MAX_TOKENS=8192
GEMINI_TEMPERATURE=0.1
GEMINI_TOP_P=0.8
GEMINI_TOP_K=40

# OCR特定配置
OCR_MAX_IMAGE_SIZE=2048
OCR_BATCH_SIZE=3
OCR_RETRY_ATTEMPTS=3
OCR_TIMEOUT=60

# AI服务配置
AI_GRADING_ENABLED=true
AI_ANALYSIS_ENABLED=true
AI_SUGGESTION_ENABLED=true

# 日志配置
LOG_LEVEL="INFO"
LOG_FILE="./logs/app.log"

# 监控配置
SENTRY_DSN=""
ENABLE_METRICS=true

# WebSocket配置
WS_URL="ws://localhost:8000"