# FastAPI核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.7  # PostgreSQL
aiosqlite==0.19.0       # SQLite异步支持

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
python-dotenv==1.0.0

# 限流和安全
slowapi==0.1.9
redis==5.0.1

# HTTP客户端
httpx==0.25.1
aiohttp==3.8.6

# 文件处理
pillow==10.0.1
python-magic==0.4.27

# Gemini AI
google-generativeai==0.3.0

# 异步任务队列
celery==5.3.4
redis==5.0.1

# 数据验证
pydantic[email]==2.4.2
email-validator==2.1.0

# 邮件服务
aiosmtplib==3.0.1
jinja2==3.1.2

# 日志和监控
sentry-sdk[fastapi]==1.38.0
prometheus-client==0.19.0

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.10.1
isort==5.12.0
flake8==6.1.0

# 文档
mkdocs==1.5.3
mkdocs-material==9.4.7