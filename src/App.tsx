import React, { useState, useEffect } from 'react';
import { ConfigProvider, App as AntdA<PERSON>, Button, Card, Layout } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AppProvider, useAppContext } from './contexts/AppContext';
import { AuthProvider } from './hooks/useAuth';
import ErrorBoundary from './components/common/ErrorBoundary';
import ErrorMonitor from './components/debug/ErrorMonitor';
import SimpleDebugToolbar from './components/debug/SimpleDebugToolbar';
import './styles/app.css';
import './utils/devtools'; // 导入开发者工具（包含geminiTest）
import './utils/logSync'; // 导入日志同步服务
import LandingPage from './components/views/LandingPage';
import DashboardView from './components/views/DashboardView';
import ExamManagementView from './components/views/ExamManagementView';



// 简单的Header测试组件
const TestHeader: React.FC = () => {
  const { currentView, setCurrentView } = useAppContext();

  return (
    <Layout.Header style={{
      background: '#fff',
      padding: '0 20px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <h2 style={{ margin: 0, color: '#1677ff' }}>智阅AI</h2>
        <span style={{ marginLeft: '20px', color: '#666' }}>
          当前视图: {currentView}
        </span>
      </div>

      <div>
        <Button
          type={currentView === 'landing' ? 'primary' : 'default'}
          style={{ marginRight: '8px' }}
          onClick={() => setCurrentView('landing')}
        >
          首页
        </Button>
        <Button
          type={currentView === 'dashboard' ? 'primary' : 'default'}
          style={{ marginRight: '8px' }}
          onClick={() => setCurrentView('dashboard')}
        >
          仪表板
        </Button>
        <Button
          type={currentView === 'exam' ? 'primary' : 'default'}
          style={{ marginRight: '8px' }}
          onClick={() => setCurrentView('exam')}
        >
          考试管理
        </Button>
        <Button
          type={currentView === 'analysis' ? 'primary' : 'default'}
          onClick={() => setCurrentView('analysis')}
        >
          数据分析
        </Button>
      </div>
    </Layout.Header>
  );
};

// 简单的内联ContentRouter
const SimpleContentRouter: React.FC = () => {
  const { currentView } = useAppContext();

  const renderContent = () => {
    switch (currentView) {
      case 'landing':
        return <LandingPage />;

      case 'dashboard':
        return <DashboardView />;

      case 'exam':
        return <ExamManagementView />;

      case 'analysis':
        return (
          <Card title="📈 数据分析" style={{ textAlign: 'center' }}>
            <h2>数据分析</h2>
            <p>查看考试数据和统计分析</p>
            <Button type="primary">生成报告</Button>
          </Card>
        );

      default:
        return (
          <Card title="❓ 未知视图" style={{ textAlign: 'center' }}>
            <h2>视图: {currentView}</h2>
            <p>这个视图还没有实现</p>
          </Card>
        );
    }
  };

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      <Card
        title="ContentRouter 状态"
        style={{ marginBottom: '20px' }}
        size="small"
      >
        <p>当前视图: <strong>{currentView}</strong></p>
        <p>ContentRouter 状态: 正常工作 ✅</p>
      </Card>

      <div style={{ minHeight: '400px' }}>
        {renderContent()}
      </div>
    </div>
  );
};

const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  const [showErrorMonitor, setShowErrorMonitor] = useState(false);
  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    // 连接到开发者工具的错误计数（如果可用）
    if (import.meta.env.DEV && window.__DEV_TOOLS__) {
      // 设置错误计数变化回调
      window.__DEV_TOOLS__.onErrorCountChange = (count: number) => {
        setErrorCount(count);
      };

      // 获取初始错误计数
      const initialCount = window.__DEV_TOOLS__.getErrorCount();
      setErrorCount(initialCount);
    }
  }, []);

  const handleToggleErrorMonitor = () => {
    setShowErrorMonitor(!showErrorMonitor);
  };

  const handleClearErrors = () => {
    if (window.__DEV_TOOLS__) {
      window.__DEV_TOOLS__.clearErrors();
    }
    setErrorCount(0);
    setShowErrorMonitor(false);
  };

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#1677ff',
            colorSuccess: '#52c41a',
            colorWarning: '#faad14',
            colorError: '#ff4d4f',
            fontSize: 14,
            borderRadius: 6,
          },
        }}
      >
        <AntdApp>
          <AuthProvider>
            <AppProvider>
              <Layout className="min-h-screen">
                <TestHeader />

                <Layout.Content style={{
                  padding: '20px',
                  backgroundColor: '#f0f2f5'
                }}>
                  <SimpleContentRouter />
                </Layout.Content>
              </Layout>

              {/* 开发环境下显示调试工具 */}
              {import.meta.env.DEV && (
                <>
                  {showErrorMonitor && <ErrorMonitor />}
                  <SimpleDebugToolbar
                    errorCount={errorCount}
                    onToggleErrorMonitor={handleToggleErrorMonitor}
                    onClearErrors={handleClearErrors}
                  />
                </>
              )}
            </AppProvider>
          </AuthProvider>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;