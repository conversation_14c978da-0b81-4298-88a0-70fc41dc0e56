import React from 'react';



function App() {
  console.log('🚀 App component is rendering!');

  return React.createElement('div', {
    style: {
      padding: '40px',
      textAlign: 'center',
      backgroundColor: '#f0f2f5',
      minHeight: '100vh',
      fontFamily: 'Arial, sans-serif'
    }
  }, [
    React.createElement('h1', {
      key: 'title',
      style: { color: '#1677ff', fontSize: '3rem', margin: '0 0 20px 0' }
    }, '🎉 智阅AI 正在运行！'),

    React.createElement('p', {
      key: 'message',
      style: { fontSize: '1.5rem', color: '#333', margin: '20px 0' }
    }, '如果您看到这个页面，说明React应用已经成功启动'),

    React.createElement('div', {
      key: 'info',
      style: {
        backgroundColor: '#fff',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        maxWidth: '600px',
        margin: '20px auto'
      }
    }, [
      React.createElement('h3', { key: 'debug-title' }, '调试信息'),
      React.createElement('p', { key: 'time' }, `当前时间: ${new Date().toLocaleString()}`),
      React.createElement('p', { key: 'status' }, 'React 状态: 正常运行 ✅')
    ])
  ]);
}

export default App;