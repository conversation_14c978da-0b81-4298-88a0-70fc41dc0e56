import React from 'react';
import { ConfigProvider, App as AntdApp, But<PERSON>, Card, Layout } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AppProvider, useAppContext } from './contexts/AppContext';
import { AuthProvider } from './hooks/useAuth';



// 简单的Header测试组件
const TestHeader: React.FC = () => {
  const { currentView, setCurrentView } = useAppContext();

  return (
    <Layout.Header style={{
      background: '#fff',
      padding: '0 20px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <h2 style={{ margin: 0, color: '#1677ff' }}>智阅AI</h2>
        <span style={{ marginLeft: '20px', color: '#666' }}>
          当前视图: {currentView}
        </span>
      </div>

      <div>
        <Button
          type={currentView === 'landing' ? 'primary' : 'default'}
          style={{ marginRight: '10px' }}
          onClick={() => setCurrentView('landing')}
        >
          首页
        </Button>
        <Button
          type={currentView === 'dashboard' ? 'primary' : 'default'}
          onClick={() => setCurrentView('dashboard')}
        >
          仪表板
        </Button>
      </div>
    </Layout.Header>
  );
};

// 简单的内联ContentRouter
const SimpleContentRouter: React.FC = () => {
  const { currentView } = useAppContext();

  const renderContent = () => {
    switch (currentView) {
      case 'landing':
        return (
          <Card title="🏠 首页" style={{ textAlign: 'center' }}>
            <h2>欢迎使用智阅AI</h2>
            <p>智能历史阅卷助手，让阅卷更高效、更准确</p>
            <div style={{ marginTop: '20px' }}>
              <Button type="primary" size="large">
                开始使用
              </Button>
            </div>
          </Card>
        );

      case 'dashboard':
        return (
          <Card title="📊 仪表板" style={{ textAlign: 'center' }}>
            <h2>系统概览</h2>
            <p>查看系统状态和统计信息</p>
            <div style={{ display: 'flex', justifyContent: 'space-around', marginTop: '20px' }}>
              <Card size="small" style={{ width: '150px' }}>
                <h3>考试总数</h3>
                <p style={{ fontSize: '24px', color: '#1677ff' }}>12</p>
              </Card>
              <Card size="small" style={{ width: '150px' }}>
                <h3>已阅卷数</h3>
                <p style={{ fontSize: '24px', color: '#52c41a' }}>8</p>
              </Card>
              <Card size="small" style={{ width: '150px' }}>
                <h3>待处理</h3>
                <p style={{ fontSize: '24px', color: '#faad14' }}>4</p>
              </Card>
            </div>
          </Card>
        );

      case 'exam':
        return (
          <Card title="📝 考试管理" style={{ textAlign: 'center' }}>
            <h2>考试管理</h2>
            <p>创建和管理考试</p>
            <Button type="primary">创建新考试</Button>
          </Card>
        );

      case 'analysis':
        return (
          <Card title="📈 数据分析" style={{ textAlign: 'center' }}>
            <h2>数据分析</h2>
            <p>查看考试数据和统计分析</p>
            <Button type="primary">生成报告</Button>
          </Card>
        );

      default:
        return (
          <Card title="❓ 未知视图" style={{ textAlign: 'center' }}>
            <h2>视图: {currentView}</h2>
            <p>这个视图还没有实现</p>
          </Card>
        );
    }
  };

  return (
    <div style={{ maxWidth: '800px', margin: '0 auto' }}>
      <Card
        title="ContentRouter 状态"
        style={{ marginBottom: '20px' }}
        size="small"
      >
        <p>当前视图: <strong>{currentView}</strong></p>
        <p>ContentRouter 状态: 正常工作 ✅</p>
      </Card>

      <div style={{ minHeight: '400px' }}>
        {renderContent()}
      </div>
    </div>
  );
};

const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1677ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
          fontSize: 14,
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <AuthProvider>
          <AppProvider>
            <Layout className="min-h-screen">
              <TestHeader />

              <Layout.Content style={{
                padding: '20px',
                backgroundColor: '#f0f2f5'
              }}>
                <SimpleContentRouter />
              </Layout.Content>
            </Layout>
          </AppProvider>
        </AuthProvider>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;