import React, { useState, useEffect } from 'react';
import { ConfigProvider, App as AntdApp, Layout } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './hooks/useAuth';
import { AppProvider, useAppContext } from './contexts/AppContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import MainApplication from './components/MainApplication';
import ErrorMonitor from './components/debug/ErrorMonitor';
import SimpleDebugToolbar from './components/debug/SimpleDebugToolbar';
import './styles/app.css';
import './utils/devtools'; // 导入开发者工具（包含geminiTest）
import './utils/logSync'; // 导入日志同步服务

// 测试版本的MainApplication
const TestMainApplication: React.FC = () => {
  console.log('TestMainApplication rendering');

  try {
    const { currentView } = useAppContext();
    console.log('Current view:', currentView);

    return (
      <Layout className="min-h-screen bg-gray-50">
        <Layout.Header style={{ background: '#fff', padding: '0 20px' }}>
          <h2>智阅AI - 测试版本</h2>
        </Layout.Header>
        <Layout.Content style={{ padding: '20px' }}>
          <div style={{ textAlign: 'center' }}>
            <h1>MainApplication 基础结构测试</h1>
            <p>当前视图: {currentView}</p>
            <p>如果看到这个，说明 MainApplication 基础结构正常</p>
          </div>
        </Layout.Content>
      </Layout>
    );
  } catch (error) {
    console.error('TestMainApplication error:', error);
    return (
      <div style={{ padding: '20px', color: 'red' }}>
        <h1>MainApplication 错误</h1>
        <p>错误: {error instanceof Error ? error.message : String(error)}</p>
      </div>
    );
  }
};

const App: React.FC = () => {
  const [showErrorMonitor, setShowErrorMonitor] = useState(false);
  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    // 连接到开发者工具的错误计数（如果可用）
    if (import.meta.env.DEV && window.__DEV_TOOLS__) {
      // 设置错误计数变化回调
      window.__DEV_TOOLS__.onErrorCountChange = (count: number) => {
        setErrorCount(count);
      };

      // 获取初始错误计数
      const initialCount = window.__DEV_TOOLS__.getErrorCount();
      setErrorCount(initialCount);
    }
  }, []);

  const handleToggleErrorMonitor = () => {
    setShowErrorMonitor(!showErrorMonitor);
  };

  const handleClearErrors = () => {
    if (window.__DEV_TOOLS__) {
      window.__DEV_TOOLS__.clearErrors();
    }
    setErrorCount(0);
    setShowErrorMonitor(false);
  };

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#1677ff',
            colorSuccess: '#52c41a',
            colorWarning: '#faad14',
            colorError: '#ff4d4f',
            fontSize: 14,
            borderRadius: 6,
          },
        }}
      >
        <AntdApp>
          <AuthProvider>
            <AppProvider>
              <MainApplication />
              {/* 开发环境下显示调试工具 */}
              {import.meta.env.DEV && (
                <>
                  {showErrorMonitor && <ErrorMonitor />}
                  <SimpleDebugToolbar
                    errorCount={errorCount}
                    onToggleErrorMonitor={handleToggleErrorMonitor}
                    onClearErrors={handleClearErrors}
                  />
                </>
              )}
            </AppProvider>
          </AuthProvider>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;