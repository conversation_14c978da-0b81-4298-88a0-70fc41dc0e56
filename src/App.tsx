import React from 'react';
import { ConfigProvider, App as AntdApp, But<PERSON>, Card, Layout } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AppProvider, useAppContext } from './contexts/AppContext';
import { AuthProvider } from './hooks/useAuth';



// 简单的Header测试组件
const TestHeader: React.FC = () => {
  const { currentView, setCurrentView } = useAppContext();

  return (
    <Layout.Header style={{
      background: '#fff',
      padding: '0 20px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between'
    }}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <h2 style={{ margin: 0, color: '#1677ff' }}>智阅AI</h2>
        <span style={{ marginLeft: '20px', color: '#666' }}>
          当前视图: {currentView}
        </span>
      </div>

      <div>
        <Button
          type={currentView === 'landing' ? 'primary' : 'default'}
          style={{ marginRight: '10px' }}
          onClick={() => setCurrentView('landing')}
        >
          首页
        </Button>
        <Button
          type={currentView === 'dashboard' ? 'primary' : 'default'}
          onClick={() => setCurrentView('dashboard')}
        >
          仪表板
        </Button>
      </div>
    </Layout.Header>
  );
};

// 测试Context的组件
const TestContextComponent: React.FC = () => {
  try {
    const { currentView, setCurrentView } = useAppContext();

    return (
      <Card
        title="Context 测试"
        style={{ maxWidth: '600px', margin: '20px auto' }}
      >
        <p>当前视图: <strong>{currentView}</strong></p>
        <p>Context 状态: 正常工作 ✅</p>
        <p>Header 组件: 正常工作 ✅</p>

        <div style={{ marginTop: '20px' }}>
          <Button
            type="primary"
            style={{ marginRight: '10px' }}
            onClick={() => setCurrentView('exam')}
          >
            切换到考试管理
          </Button>
          <Button
            onClick={() => setCurrentView('analysis')}
          >
            切换到数据分析
          </Button>
        </div>
      </Card>
    );
  } catch (error) {
    return (
      <Card
        title="Context 错误"
        style={{ maxWidth: '600px', margin: '20px auto', borderColor: '#ff4d4f' }}
      >
        <p style={{ color: '#ff4d4f' }}>
          Context 错误: {error instanceof Error ? error.message : String(error)}
        </p>
      </Card>
    );
  }
};

const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1677ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
          fontSize: 14,
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <AuthProvider>
          <AppProvider>
            <Layout className="min-h-screen">
              <TestHeader />

              <Layout.Content style={{
                padding: '40px',
                textAlign: 'center',
                backgroundColor: '#f0f2f5'
              }}>
                <h1 style={{ color: '#1677ff', fontSize: '3rem', margin: '0 0 20px 0' }}>
                  🎉 智阅AI 正在运行！
                </h1>

                <p style={{ fontSize: '1.5rem', color: '#333', margin: '20px 0' }}>
                  现在有了完整的布局结构
                </p>

                <Card
                  title="基础调试信息"
                  style={{ maxWidth: '600px', margin: '20px auto' }}
                >
                  <p>当前时间: {new Date().toLocaleString()}</p>
                  <p>React 状态: 正常运行 ✅</p>
                  <p>JSX 语法: 正常工作 ✅</p>
                  <p>Ant Design: 正常加载 ✅</p>
                  <p>Layout 结构: 正常工作 ✅</p>
                </Card>

                <TestContextComponent />
              </Layout.Content>
            </Layout>
          </AppProvider>
        </AuthProvider>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;