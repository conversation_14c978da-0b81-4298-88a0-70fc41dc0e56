import React from 'react';
import { ConfigProvider, App as AntdA<PERSON>, <PERSON><PERSON>, Card } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AppProvider, useAppContext } from './contexts/AppContext';



// 测试Context的组件
const TestContextComponent: React.FC = () => {
  try {
    const { currentView, setCurrentView } = useAppContext();

    return (
      <Card
        title="Context 测试"
        style={{ maxWidth: '600px', margin: '20px auto' }}
      >
        <p>当前视图: <strong>{currentView}</strong></p>
        <p>Context 状态: 正常工作 ✅</p>

        <div style={{ marginTop: '20px' }}>
          <Button
            type="primary"
            style={{ marginRight: '10px' }}
            onClick={() => setCurrentView('dashboard')}
          >
            切换到仪表板
          </Button>
          <Button
            onClick={() => setCurrentView('landing')}
          >
            切换到首页
          </Button>
        </div>
      </Card>
    );
  } catch (error) {
    return (
      <Card
        title="Context 错误"
        style={{ maxWidth: '600px', margin: '20px auto', borderColor: '#ff4d4f' }}
      >
        <p style={{ color: '#ff4d4f' }}>
          Context 错误: {error instanceof Error ? error.message : String(error)}
        </p>
      </Card>
    );
  }
};

const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1677ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
          fontSize: 14,
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <AppProvider>
          <div style={{
            padding: '40px',
            textAlign: 'center',
            backgroundColor: '#f0f2f5',
            minHeight: '100vh',
            fontFamily: 'Arial, sans-serif'
          }}>
            <h1 style={{ color: '#1677ff', fontSize: '3rem', margin: '0 0 20px 0' }}>
              🎉 智阅AI 正在运行！
            </h1>

            <p style={{ fontSize: '1.5rem', color: '#333', margin: '20px 0' }}>
              如果您看到这个页面，说明React应用已经成功启动
            </p>

            <Card
              title="基础调试信息"
              style={{ maxWidth: '600px', margin: '20px auto' }}
            >
              <p>当前时间: {new Date().toLocaleString()}</p>
              <p>React 状态: 正常运行 ✅</p>
              <p>JSX 语法: 正常工作 ✅</p>
              <p>Ant Design: 正常加载 ✅</p>
            </Card>

            <TestContextComponent />
          </div>
        </AppProvider>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;