import React from 'react';
import { ConfigProvider, App as AntdApp, <PERSON><PERSON>, Card } from 'antd';
import zhCN from 'antd/locale/zh_CN';



const App: React.FC = () => {
  console.log('🚀 App component is rendering!');

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        token: {
          colorPrimary: '#1677ff',
          colorSuccess: '#52c41a',
          colorWarning: '#faad14',
          colorError: '#ff4d4f',
          fontSize: 14,
          borderRadius: 6,
        },
      }}
    >
      <AntdApp>
        <div style={{
          padding: '40px',
          textAlign: 'center',
          backgroundColor: '#f0f2f5',
          minHeight: '100vh',
          fontFamily: 'Arial, sans-serif'
        }}>
          <h1 style={{ color: '#1677ff', fontSize: '3rem', margin: '0 0 20px 0' }}>
            🎉 智阅AI 正在运行！
          </h1>

          <p style={{ fontSize: '1.5rem', color: '#333', margin: '20px 0' }}>
            如果您看到这个页面，说明React应用已经成功启动
          </p>

          <Card
            title="调试信息"
            style={{ maxWidth: '600px', margin: '20px auto' }}
          >
            <p>当前时间: {new Date().toLocaleString()}</p>
            <p>React 状态: 正常运行 ✅</p>
            <p>JSX 语法: 正常工作 ✅</p>
            <p>Ant Design: 正常加载 ✅</p>

            <div style={{ marginTop: '20px' }}>
              <Button type="primary" style={{ marginRight: '10px' }}>
                主要按钮
              </Button>
              <Button>
                默认按钮
              </Button>
            </div>
          </Card>
        </div>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;