import React, { useState, useEffect } from 'react';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './hooks/useAuth';
import { AppProvider } from './contexts/AppContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import MainApplication from './components/MainApplication';
import ErrorMonitor from './components/debug/ErrorMonitor';
import SimpleDebugToolbar from './components/debug/SimpleDebugToolbar';
import './styles/app.css';
// 暂时注释掉可能有问题的导入
// import './utils/devtools'; // 导入开发者工具（包含geminiTest）
// import './utils/logSync'; // 导入日志同步服务

const App: React.FC = () => {
  const [showErrorMonitor, setShowErrorMonitor] = useState(false);
  const [errorCount, setErrorCount] = useState(0);

  useEffect(() => {
    // 暂时不连接开发者工具，避免错误
    console.log('App component mounted');
  }, []);

  const handleToggleErrorMonitor = () => {
    setShowErrorMonitor(!showErrorMonitor);
  };

  const handleClearErrors = () => {
    setErrorCount(0);
    setShowErrorMonitor(false);
  };

  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#1677ff',
            colorSuccess: '#52c41a',
            colorWarning: '#faad14',
            colorError: '#ff4d4f',
            fontSize: 14,
            borderRadius: 6,
          },
        }}
      >
        <AntdApp>
          <AuthProvider>
            <div style={{
              padding: '20px',
              textAlign: 'center',
              backgroundColor: '#f0f2f5',
              minHeight: '100vh'
            }}>
              <h1>测试 AuthProvider</h1>
              <p>如果看到这个，说明 AuthProvider 工作正常</p>
              <p>现在测试 AppProvider...</p>
            </div>
          </AuthProvider>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;