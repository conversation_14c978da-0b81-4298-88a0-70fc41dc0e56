import React, { useState, useEffect } from 'react';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { AuthProvider } from './hooks/useAuth';
import { AppProvider } from './contexts/AppContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import MainApplication from './components/MainApplication';
import ErrorMonitor from './components/debug/ErrorMonitor';
import SimpleDebugToolbar from './components/debug/SimpleDebugToolbar';
import './styles/app.css';
// 暂时注释掉可能有问题的导入
// import './utils/devtools'; // 导入开发者工具（包含geminiTest）
// import './utils/logSync'; // 导入日志同步服务

const App: React.FC = () => {
  // 添加调试信息
  console.log('App component is rendering');

  useEffect(() => {
    console.log('App component mounted');
    return () => {
      console.log('App component unmounted');
    };
  }, []);

  // 简化版本用于调试
  return (
    <ErrorBoundary>
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#1677ff',
            colorSuccess: '#52c41a',
            colorWarning: '#faad14',
            colorError: '#ff4d4f',
            fontSize: 14,
            borderRadius: 6,
          },
        }}
      >
        <AntdApp>
          <div style={{
            padding: '20px',
            textAlign: 'center',
            backgroundColor: '#f0f2f5',
            minHeight: '100vh',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <h1 style={{ color: '#1677ff', fontSize: '2rem', marginBottom: '1rem' }}>
              智阅AI 测试页面
            </h1>
            <p style={{ fontSize: '1.2rem', color: '#666' }}>
              如果您看到这个页面，说明基础组件正常工作
            </p>
            <p style={{ fontSize: '1rem', color: '#999', marginTop: '1rem' }}>
              时间: {new Date().toLocaleString()}
            </p>
          </div>
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
};

export default App;