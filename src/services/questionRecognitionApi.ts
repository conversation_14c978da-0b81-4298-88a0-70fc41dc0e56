// 试题识别与分析API服务
import { geminiGradingApi } from './geminiApi';
import { message } from 'antd';

export interface RecognizedQuestion {
  id: string;
  number: string;
  type: 'choice' | 'fill' | 'short_answer' | 'essay' | 'analysis';
  content: string;
  points: number;
  difficulty: 'easy' | 'medium' | 'hard';
  knowledgePoints: string[];
  region: {
    page: number;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  subQuestions?: RecognizedQuestion[];
}

export interface RecognitionResult {
  questions: RecognizedQuestion[];
  totalQuestions: number;
  totalPoints: number;
  questionTypes: Record<string, number>;
  processingTime: number;
  confidence: number;
}

export interface RubricDimension {
  id: string;
  name: string;
  description: string;
  maxPoints: number;
  criteria: string[];
  keywords: string[];
  weight: number;
}

export interface AutoGeneratedRubric {
  questionId: string;
  dimensions: RubricDimension[];
  totalPoints: number;
  evaluationMethod: 'holistic' | 'analytic';
  aiGenerated: boolean;
}

class QuestionRecognitionService {
  // 分析上传的试卷文档
  async analyzeDocument(file: File): Promise<RecognitionResult> {
    try {
      message.loading('AI正在分析试卷结构...', 0);
      
      // 模拟OCR文本提取
      const extractedText = await this.extractTextFromDocument(file);
      
      // 使用Gemini AI进行试题识别
      const recognitionResult = await this.recognizeQuestions(extractedText);
      
      message.destroy();
      message.success(`成功识别${recognitionResult.totalQuestions}道题目`);
      
      return recognitionResult;
    } catch (error) {
      message.destroy();
      message.error('试题识别失败，请检查文档质量');
      throw error;
    }
  }

  // 从文档中提取文本（模拟OCR）
  private async extractTextFromDocument(file: File): Promise<string> {
    // 这里应该调用实际的OCR服务
    // 目前返回模拟数据
    return `
      2024-2025学年第二学期八年级历史期末考试

      一、选择题（每题2分，共20分）
      1. 中国古代四大发明中，最早传入欧洲的是（）
      A. 造纸术  B. 指南针  C. 火药  D. 印刷术

      2. 下列关于秦始皇统一中国的措施，正确的是（）
      A. 统一货币  B. 统一文字  C. 统一度量衡  D. 以上都对

      二、填空题（每空1分，共10分）
      3. 中国古代的"四书五经"中，"四书"指的是《论语》、《孟子》、《大学》和_______。

      4. 唐朝时期，_______是当时世界上最大的城市。

      三、简答题（每题8分，共16分）
      5. 简述秦始皇统一中国的历史意义。

      6. 分析唐朝"贞观之治"出现的原因。

      四、材料分析题（14分）
      7. 阅读下列材料，回答问题：
      材料一：秦王扫六合，虎视何雄哉！挥剑决浮云，诸侯尽西来。
      材料二：秦始皇统一中国后，采取了一系列巩固统治的措施...

      （1）材料一反映了什么历史事件？（4分）
      （2）结合材料二，分析秦始皇巩固统治的主要措施。（6分）
      （3）评价秦始皇统一中国的历史作用。（4分）
    `;
  }

  // 使用AI识别试题
  private async recognizeQuestions(text: string): Promise<RecognitionResult> {
    const prompt = `
请分析以下历史试卷内容，识别并提取所有题目信息：

${text}

请按照以下JSON格式返回结果：
{
  "questions": [
    {
      "id": "q1",
      "number": "1",
      "type": "choice|fill|short_answer|essay|analysis",
      "content": "题目完整内容",
      "points": 分值,
      "difficulty": "easy|medium|hard",
      "knowledgePoints": ["知识点1", "知识点2"],
      "region": {
        "page": 1,
        "x": 0,
        "y": 0,
        "width": 100,
        "height": 50
      },
      "subQuestions": [子题目数组，如果有的话]
    }
  ],
  "totalQuestions": 总题数,
  "totalPoints": 总分,
  "questionTypes": {
    "choice": 选择题数量,
    "fill": 填空题数量,
    "short_answer": 简答题数量,
    "essay": 论述题数量,
    "analysis": 材料分析题数量
  },
  "confidence": 识别置信度(0-100)
}

注意：
1. 准确识别题目类型和分值
2. 提取完整的题目内容
3. 识别相关的历史知识点
4. 对于材料分析题，要识别子问题
5. 评估题目难度等级
`;

    try {
      // 检查是否有Gemini API配置
      const hasGeminiAPI = import.meta.env.VITE_GEMINI_API_KEY;

      if (hasGeminiAPI) {
        try {
          const response = await geminiGradingApi.analyzePaperContent(prompt);
          // 模拟处理时间
          await new Promise(resolve => setTimeout(resolve, 2000));
          return response;
        } catch (apiError) {
          console.warn('Gemini API failed, falling back to mock data:', apiError);
          message.warning('AI服务暂时不可用，使用模拟数据进行演示');
        }
      } else {
        console.warn('Gemini API key not configured, using mock data');
        message.info('AI服务未配置，使用模拟数据进行演示');
      }

      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 返回模拟数据作为fallback
      return {
        questions: [
          {
            id: 'q1',
            number: '1',
            type: 'choice',
            content: '中国古代四大发明中，最早传入欧洲的是（）\nA. 造纸术  B. 指南针  C. 火药  D. 印刷术',
            points: 2,
            difficulty: 'easy',
            knowledgePoints: ['古代科技', '四大发明', '中外交流'],
            region: { page: 1, x: 10, y: 15, width: 80, height: 8 }
          },
          {
            id: 'q2',
            number: '2',
            type: 'choice',
            content: '下列关于秦始皇统一中国的措施，正确的是（）\nA. 统一货币  B. 统一文字  C. 统一度量衡  D. 以上都对',
            points: 2,
            difficulty: 'medium',
            knowledgePoints: ['秦朝历史', '政治制度', '文化统一'],
            region: { page: 1, x: 10, y: 25, width: 80, height: 8 }
          },
          {
            id: 'q5',
            number: '5',
            type: 'short_answer',
            content: '简述秦始皇统一中国的历史意义。',
            points: 8,
            difficulty: 'medium',
            knowledgePoints: ['秦朝历史', '历史意义', '政治统一'],
            region: { page: 1, x: 10, y: 50, width: 80, height: 15 }
          },
          {
            id: 'q7',
            number: '7',
            type: 'analysis',
            content: '阅读下列材料，回答问题：\n材料一：秦王扫六合，虎视何雄哉！挥剑决浮云，诸侯尽西来。\n材料二：秦始皇统一中国后，采取了一系列巩固统治的措施...',
            points: 14,
            difficulty: 'hard',
            knowledgePoints: ['秦朝历史', '史料分析', '历史评价'],
            region: { page: 1, x: 10, y: 70, width: 80, height: 25 },
            subQuestions: [
              {
                id: 'q7_1',
                number: '(1)',
                type: 'short_answer',
                content: '材料一反映了什么历史事件？',
                points: 4,
                difficulty: 'medium',
                knowledgePoints: ['秦朝历史', '史料分析'],
                region: { page: 1, x: 15, y: 85, width: 75, height: 5 }
              },
              {
                id: 'q7_2',
                number: '(2)',
                type: 'short_answer',
                content: '结合材料二，分析秦始皇巩固统治的主要措施。',
                points: 6,
                difficulty: 'hard',
                knowledgePoints: ['秦朝历史', '政治制度', '史料分析'],
                region: { page: 1, x: 15, y: 90, width: 75, height: 5 }
              },
              {
                id: 'q7_3',
                number: '(3)',
                type: 'essay',
                content: '评价秦始皇统一中国的历史作用。',
                points: 4,
                difficulty: 'hard',
                knowledgePoints: ['秦朝历史', '历史评价', '批判思维'],
                region: { page: 1, x: 15, y: 95, width: 75, height: 5 }
              }
            ]
          }
        ],
        totalQuestions: 7,
        totalPoints: 60,
        questionTypes: {
          choice: 2,
          fill: 2,
          short_answer: 2,
          essay: 0,
          analysis: 1
        },
        processingTime: 3500,
        confidence: 92
      };
    } catch (error) {
      console.error('Question recognition failed:', error);
      throw new Error('试题识别失败');
    }
  }

  // 自动生成多维评分标准
  async generateRubric(question: RecognizedQuestion, referenceAnswer?: string): Promise<AutoGeneratedRubric> {
    try {
      const prompt = `
请为以下历史题目生成多维评分标准：

题目信息：
- 题目类型：${question.type}
- 题目内容：${question.content}
- 总分：${question.points}分
- 知识点：${question.knowledgePoints.join('、')}
- 难度：${question.difficulty}
${referenceAnswer ? `- 参考答案：${referenceAnswer}` : ''}

请生成符合历史学科特点的多维评分标准，包含以下维度：
1. 历史知识掌握（基础知识点的准确性）
2. 史料运用能力（史实引用的准确性和相关性）
3. 逻辑论证能力（论述的逻辑性和条理性）
4. 历史思维能力（分析、综合、评价能力）
5. 语言表达能力（表述的清晰度和规范性）

请按照以下JSON格式返回：
{
  "questionId": "${question.id}",
  "dimensions": [
    {
      "id": "knowledge",
      "name": "历史知识掌握",
      "description": "评估学生对相关历史知识点的掌握程度",
      "maxPoints": 分值,
      "criteria": ["评分标准1", "评分标准2"],
      "keywords": ["关键词1", "关键词2"],
      "weight": 权重(0-1)
    }
  ],
  "totalPoints": ${question.points},
  "evaluationMethod": "analytic",
  "aiGenerated": true
}
`;

      // 根据题目类型生成不同的评分标准
      if (question.type === 'choice' || question.type === 'fill') {
        // 客观题使用简化的评分标准
        return {
          questionId: question.id,
          dimensions: [
            {
              id: 'accuracy',
              name: '答案准确性',
              description: '答案是否正确',
              maxPoints: question.points,
              criteria: ['答案完全正确', '答案部分正确', '答案错误'],
              keywords: [],
              weight: 1.0
            }
          ],
          totalPoints: question.points,
          evaluationMethod: 'holistic',
          aiGenerated: true
        };
      }

      // 主观题使用多维评分标准
      const dimensions: RubricDimension[] = [];
      const pointsPerDimension = Math.floor(question.points / 4);
      const remainingPoints = question.points % 4;

      // 历史知识掌握
      dimensions.push({
        id: 'knowledge',
        name: '历史知识掌握',
        description: '评估学生对相关历史知识点的掌握程度和准确性',
        maxPoints: pointsPerDimension + (remainingPoints > 0 ? 1 : 0),
        criteria: [
          '知识点完全正确，表述准确',
          '知识点基本正确，有少量错误',
          '知识点部分正确，有明显错误',
          '知识点错误或缺失'
        ],
        keywords: question.knowledgePoints,
        weight: 0.3
      });

      // 史料运用能力
      dimensions.push({
        id: 'evidence',
        name: '史料运用能力',
        description: '评估学生运用史实和史料支撑观点的能力',
        maxPoints: pointsPerDimension + (remainingPoints > 1 ? 1 : 0),
        criteria: [
          '史料运用准确，与论点高度契合',
          '史料运用基本准确，与论点相关',
          '史料运用部分准确，相关性一般',
          '史料运用错误或无关'
        ],
        keywords: ['史实', '史料', '证据', '例证'],
        weight: 0.25
      });

      // 逻辑论证能力
      dimensions.push({
        id: 'logic',
        name: '逻辑论证能力',
        description: '评估学生论述的逻辑性、条理性和说服力',
        maxPoints: pointsPerDimension + (remainingPoints > 2 ? 1 : 0),
        criteria: [
          '逻辑清晰，论证严密，条理分明',
          '逻辑基本清晰，论证较为合理',
          '逻辑一般，论证有缺陷',
          '逻辑混乱，论证不充分'
        ],
        keywords: ['因此', '所以', '由于', '导致', '影响'],
        weight: 0.25
      });

      // 语言表达能力
      dimensions.push({
        id: 'expression',
        name: '语言表达能力',
        description: '评估学生语言表述的准确性、规范性和流畅性',
        maxPoints: pointsPerDimension,
        criteria: [
          '表述准确规范，语言流畅',
          '表述基本准确，语言较为流畅',
          '表述一般，有语言错误',
          '表述不清，语言错误较多'
        ],
        keywords: ['表述', '语言', '用词'],
        weight: 0.2
      });

      return {
        questionId: question.id,
        dimensions,
        totalPoints: question.points,
        evaluationMethod: 'analytic',
        aiGenerated: true
      };

    } catch (error) {
      console.error('Rubric generation failed:', error);
      throw new Error('评分标准生成失败');
    }
  }

  // 批量生成评分标准
  async batchGenerateRubrics(questions: RecognizedQuestion[], referenceAnswers?: Record<string, string>): Promise<AutoGeneratedRubric[]> {
    const rubrics: AutoGeneratedRubric[] = [];
    
    for (const question of questions) {
      try {
        const referenceAnswer = referenceAnswers?.[question.id];
        const rubric = await this.generateRubric(question, referenceAnswer);
        rubrics.push(rubric);
        
        // 添加延迟避免API限流
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`Failed to generate rubric for question ${question.id}:`, error);
        // 生成默认评分标准
        rubrics.push(this.generateDefaultRubric(question));
      }
    }
    
    return rubrics;
  }

  // 生成默认评分标准
  private generateDefaultRubric(question: RecognizedQuestion): AutoGeneratedRubric {
    if (question.type === 'choice' || question.type === 'fill') {
      return {
        questionId: question.id,
        dimensions: [
          {
            id: 'accuracy',
            name: '答案准确性',
            description: '答案是否正确',
            maxPoints: question.points,
            criteria: ['正确', '错误'],
            keywords: [],
            weight: 1.0
          }
        ],
        totalPoints: question.points,
        evaluationMethod: 'holistic',
        aiGenerated: false
      };
    }

    // 主观题默认评分标准
    const pointsPerDimension = Math.floor(question.points / 3);
    const remainingPoints = question.points % 3;

    return {
      questionId: question.id,
      dimensions: [
        {
          id: 'content',
          name: '内容要点',
          description: '答案要点的完整性和准确性',
          maxPoints: pointsPerDimension + (remainingPoints > 0 ? 1 : 0),
          criteria: ['要点完整准确', '要点基本完整', '要点不完整', '要点错误'],
          keywords: question.knowledgePoints,
          weight: 0.5
        },
        {
          id: 'logic',
          name: '逻辑表述',
          description: '论述的逻辑性和条理性',
          maxPoints: pointsPerDimension + (remainingPoints > 1 ? 1 : 0),
          criteria: ['逻辑清晰', '逻辑基本清晰', '逻辑一般', '逻辑混乱'],
          keywords: [],
          weight: 0.3
        },
        {
          id: 'expression',
          name: '语言表达',
          description: '语言表述的准确性和规范性',
          maxPoints: pointsPerDimension,
          criteria: ['表述准确规范', '表述基本准确', '表述一般', '表述不清'],
          keywords: [],
          weight: 0.2
        }
      ],
      totalPoints: question.points,
      evaluationMethod: 'analytic',
      aiGenerated: false
    };
  }

  // 优化评分标准
  async optimizeRubric(rubric: AutoGeneratedRubric, feedback: string): Promise<AutoGeneratedRubric> {
    // 这里可以根据教师反馈优化评分标准
    // 目前返回原评分标准
    return rubric;
  }
}

export const questionRecognitionService = new QuestionRecognitionService();