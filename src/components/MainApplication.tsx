import React from 'react';
import { Layout } from 'antd';
import { useAppContext } from '../contexts/AppContext';
import Header from './layout/Header';
import ContentRouter from './layout/ContentRouter';

const MainApplication: React.FC = () => {
  console.log('MainApplication rendering');

  try {
    const { currentView, subViewInfo } = useAppContext();
    console.log('MainApplication - currentView:', currentView, 'subViewInfo:', subViewInfo);

    return (
      <Layout className="min-h-screen bg-gray-50">
        <Header />
        <Layout.Content className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <ContentRouter />
          </div>
        </Layout.Content>
      </Layout>
    );
  } catch (error) {
    console.error('MainApplication error:', error);
    return (
      <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
        <h1>MainApplication 错误</h1>
        <p>错误: {error instanceof Error ? error.message : String(error)}</p>
      </div>
    );
  }
};

export default MainApplication;