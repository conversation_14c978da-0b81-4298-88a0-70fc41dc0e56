import React from 'react';
import { Layout } from 'antd';
import { useAppContext } from '../contexts/AppContext';
// import Header from './layout/Header';
// import ContentRouter from './layout/ContentRouter';

const MainApplication: React.FC = () => {
  console.log('MainApplication rendering');

  try {
    const { currentView, subViewInfo } = useAppContext();
    console.log('MainApplication - currentView:', currentView, 'subViewInfo:', subViewInfo);

    return (
      <Layout className="min-h-screen bg-gray-50">
        <Layout.Header style={{ background: '#fff', padding: '0 20px', display: 'flex', alignItems: 'center' }}>
          <h2 style={{ margin: 0, color: '#1677ff' }}>智阅AI</h2>
        </Layout.Header>
        <Layout.Content className="p-4 sm:p-6 lg:p-8">
          <div className="max-w-7xl mx-auto">
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <h1>MainApplication 测试</h1>
              <p>当前视图: {currentView}</p>
              <p>子视图信息: {JSON.stringify(subViewInfo)}</p>
              <p>如果看到这个，说明 MainApplication 基础功能正常</p>
            </div>
          </div>
        </Layout.Content>
      </Layout>
    );
  } catch (error) {
    console.error('MainApplication error:', error);
    return (
      <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
        <h1>MainApplication 错误</h1>
        <p>错误: {error instanceof Error ? error.message : String(error)}</p>
      </div>
    );
  }
};

export default MainApplication;