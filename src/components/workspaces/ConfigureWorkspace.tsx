import React, { useState, useMemo } from 'react';
import { Card, Row, Col, Breadcrumb, Button, Alert, Tag, message, Upload, Progress, Spin } from 'antd';
import { CheckCircleOutlined, UploadOutlined, RobotOutlined, FileTextOutlined } from '@ant-design/icons';
import { useAppContext } from '../../contexts/AppContext';
import { Exam } from '../../types/exam';
import { mockConfigureData } from '../../data/mockData';
import QuestionRecognitionWorkspace from './QuestionRecognitionWorkspace';
import { RecognizedQuestion, AutoGeneratedRubric } from '../../services/questionRecognitionApi';

interface ConfigureWorkspaceProps {
  exam: Exam;
}

interface UploadedFile {
  name: string;
  url: string;
  type: string;
  size: number;
  originalFile: File;
}

const ConfigureWorkspace: React.FC<ConfigureWorkspaceProps> = ({ exam }) => {
  const { setSubViewInfo, updateExamStatus } = useAppContext();
  const [uploadedFiles, setUploadedFiles] = useState<{
    paper: UploadedFile | null;
    answer: UploadedFile | null;
  }>({
    paper: null,
    answer: null
  });
  const [processingStatus, setProcessingStatus] = useState({
    paper: 'none' as 'none' | 'uploading' | 'processing' | 'completed' | 'error',
    answer: 'none' as 'none' | 'uploading' | 'processing' | 'completed' | 'error'
  });
  const [showRecognitionWorkspace, setShowRecognitionWorkspace] = useState(false);
  const [recognizedQuestions, setRecognizedQuestions] = useState<RecognizedQuestion[]>([]);
  const [generatedRubrics, setGeneratedRubrics] = useState<AutoGeneratedRubric[]>([]);
  const [configurationMode, setConfigurationMode] = useState<'manual' | 'ai'>('manual');

  const handleBack = () => {
    setSubViewInfo({ view: null, exam: null });
  };

  const handleSaveConfiguration = () => {
    if (recognizedQuestions.length === 0) {
      message.warning('请先完成AI识别配置');
      return;
    }

    try {
      // 更新考试状态为"待阅卷"
      updateExamStatus(exam.id, '待阅卷');
      
      message.success('配置保存成功！考试已进入待阅卷状态');
      
      // 延迟跳转到阅卷中心
      setTimeout(() => {
        setSubViewInfo({ view: null, exam: null });
      }, 1500);
      
    } catch (error) {
      message.error('保存配置失败，请重试');
    }
  };

  // 处理文件上传
  const handleFileUpload = (type: 'paper' | 'answer') => async (info: any) => {
    const { file, fileList } = info;
    
    if (file.status === 'uploading') {
      setProcessingStatus(prev => ({ ...prev, [type]: 'uploading' }));
      return;
    }
    
    if (fileList.length === 0) {
      // 文件被移除
      setUploadedFiles(prev => ({ ...prev, [type]: null }));
      setProcessingStatus(prev => ({ ...prev, [type]: 'none' }));
      return;
    }

    const uploadedFile = fileList[fileList.length - 1];
    const fileObj = uploadedFile.originFileObj || uploadedFile;
    
    if (fileObj) {
      try {
        setProcessingStatus(prev => ({ ...prev, [type]: 'processing' }));
        
        const newFile: UploadedFile = {
          name: fileObj.name,
          url: URL.createObjectURL(fileObj),
          type: fileObj.type,
          size: fileObj.size,
          originalFile: fileObj
        };
        
        setUploadedFiles(prev => ({ ...prev, [type]: newFile }));
        
        // 模拟处理时间
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        setProcessingStatus(prev => ({ ...prev, [type]: 'completed' }));
        message.success(`${type === 'paper' ? '试卷' : '参考答案'}文件上传成功！`);
        
      } catch (error) {
        setProcessingStatus(prev => ({ ...prev, [type]: 'error' }));
        message.error(`文件处理失败: ${error}`);
      }
    }
  };

  // 删除文件
  const handleDeleteFile = (type: 'paper' | 'answer') => {
    const file = uploadedFiles[type];
    if (file?.url) {
      URL.revokeObjectURL(file.url);
    }
    setUploadedFiles(prev => ({ ...prev, [type]: null }));
    setProcessingStatus(prev => ({ ...prev, [type]: 'none' }));
    message.success(`${type === 'paper' ? '试卷' : '参考答案'}文件已删除`);
  };

  // 启动AI识别
  const startAIRecognition = () => {
    if (!uploadedFiles.paper) {
      message.warning('请先上传试卷文件');
      return;
    }
    setConfigurationMode('ai');
    setShowRecognitionWorkspace(true);
  };

  // AI识别完成回调
  const handleRecognitionComplete = (questions: RecognizedQuestion[], rubrics: AutoGeneratedRubric[]) => {
    setRecognizedQuestions(questions);
    setGeneratedRubrics(rubrics);
    setShowRecognitionWorkspace(false);
    message.success('AI识别和配置完成！现在可以保存配置并开始阅卷');
  };

  // 渲染文件上传区域
  const renderFileUploadArea = (type: 'paper' | 'answer') => {
    const file = uploadedFiles[type];
    const status = processingStatus[type];
    const title = type === 'paper' ? '试卷文件' : '参考答案';
    const description = type === 'paper' ? '上传试卷原件（PDF、JPG、PNG格式）' : '上传参考答案（可选，用于生成评分标准）';

    if (status === 'none' || !file) {
      return (
        <Card
          title={title}
          className="h-full"
          styles={{ body: { height: 'calc(100% - 57px)', display: 'flex', alignItems: 'center', justifyContent: 'center' } }}
        >
          <Upload
            name="file"
            showUploadList={false}
            beforeUpload={() => false}
            onChange={handleFileUpload(type)}
            accept=".pdf,.jpg,.jpeg,.png"
            className="w-full"
          >
            <div className="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 transition-colors cursor-pointer">
              <div className="mb-4">
                <FileTextOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
              </div>
              <p className="text-gray-600 mb-2 font-medium">{description}</p>
              <p className="text-xs text-gray-400 mb-3">
                支持 PDF、JPG、PNG 格式，最大 10MB
              </p>
              <Button type="primary" ghost icon={<UploadOutlined />}>
                选择文件
              </Button>
            </div>
          </Upload>
        </Card>
      );
    }

    if (status === 'uploading') {
      return (
        <Card title={title} className="h-full">
          <div className="h-full flex flex-col items-center justify-center bg-blue-50 rounded-lg">
            <Progress 
              type="circle" 
              percent={30}
              status="active"
              className="mb-4"
            />
            <p className="text-blue-600 font-medium">正在上传文件...</p>
            <p className="text-sm text-gray-500">{file.name}</p>
          </div>
        </Card>
      );
    }

    if (status === 'processing') {
      return (
        <Card title={title} className="h-full">
          <div className="h-full flex flex-col items-center justify-center bg-purple-50 rounded-lg">
            <Spin size="large" className="mb-4" />
            <p className="text-purple-600 font-medium">AI正在分析文件...</p>
            <p className="text-sm text-gray-500">{file.name}</p>
            <div className="mt-4 text-xs text-gray-400 text-center">
              <p>• 识别文档结构</p>
              <p>• 提取题目信息</p>
              <p>• 分析内容特征</p>
            </div>
          </div>
        </Card>
      );
    }

    if (status === 'error') {
      return (
        <Card title={title} className="h-full">
          <div className="h-full flex flex-col items-center justify-center bg-red-50 rounded-lg">
            <Alert
              message="文件处理失败"
              description="请检查文件格式是否正确，或重新上传文件"
              type="error"
              showIcon
              className="mb-4"
            />
            <Button 
              type="primary" 
              danger 
              onClick={() => handleDeleteFile(type)}
            >
              重新上传
            </Button>
          </div>
        </Card>
      );
    }

    if (status === 'completed' && file) {
      return (
        <Card 
          title={
            <div className="flex items-center justify-between">
              <span>{title}</span>
              <Tag color="green">已上传</Tag>
            </div>
          } 
          className="h-full"
        >
          <div className="space-y-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-3">
                <FileTextOutlined className="text-green-600 text-xl" />
                <div className="flex-1">
                  <p className="font-medium text-gray-800 mb-1">{file.name}</p>
                  <p className="text-sm text-gray-500">
                    大小: {(file.size / 1024 / 1024).toFixed(2)} MB | 
                    类型: {file.type}
                  </p>
                </div>
              </div>
            </div>

            <div className="flex gap-2">
              <Button 
                type="primary" 
                ghost 
                onClick={() => window.open(file.url, '_blank')}
                className="flex-1"
              >
                查看文件
              </Button>
              <Button 
                danger 
                onClick={() => handleDeleteFile(type)}
              >
                删除
              </Button>
            </div>

            {type === 'paper' && (
              <Alert
                message="文件已就绪"
                description="可以开始AI智能识别，自动生成题目和评分标准"
                type="success"
                showIcon
              />
            )}
          </div>
        </Card>
      );
    }

    return null;
  };

  // 如果显示AI识别工作台
  if (showRecognitionWorkspace && uploadedFiles.paper) {
    return (
      <div className="h-full">
        <Breadcrumb className="mb-4" items={[
          { title: <a onClick={handleBack}>考试管理</a> },
          { title: exam.name },
          { title: 'AI智能识别配置' }
        ]} />
        
        <QuestionRecognitionWorkspace
          file={uploadedFiles.paper.originalFile}
          onComplete={handleRecognitionComplete}
          onCancel={() => setShowRecognitionWorkspace(false)}
        />
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <Breadcrumb
          items={[
            { title: <a onClick={handleBack}>考试管理</a> },
            { title: exam.name },
            { title: '配置试卷' }
          ]}
        />
        <div className="flex items-center gap-2">
          {processingStatus.paper === 'completed' && recognizedQuestions.length === 0 && (
            <Button
              type="primary"
              size="large"
              icon={<RobotOutlined />}
              onClick={startAIRecognition}
            >
              开始AI智能识别
            </Button>
          )}
          <Button
            type="primary"
            size="large"
            icon={<CheckCircleOutlined />}
            onClick={handleSaveConfiguration}
            disabled={recognizedQuestions.length === 0}
          >
            保存配置并开始阅卷
          </Button>
        </div>
      </div>

      {/* 配置模式指示 */}
      {configurationMode === 'ai' && recognizedQuestions.length > 0 && (
        <Alert
          message="AI智能配置完成"
          description={`已识别 ${recognizedQuestions.length} 道题目，并生成多维评分标准。点击"保存配置并开始阅卷"即可进入阅卷流程。`}
          type="success"
          showIcon
          icon={<RobotOutlined />}
          className="mb-4"
        />
      )}

      {/* 使用说明 */}
      <Alert
        message="智能配置流程"
        description={
          <div className="space-y-2">
            <p><strong>第一步：</strong>上传试卷文件（必需）- 支持PDF、JPG、PNG格式</p>
            <p><strong>第二步：</strong>上传参考答案（可选）- 帮助AI生成更准确的评分标准</p>
            <p><strong>第三步：</strong>点击"开始AI智能识别"，系统将自动识别题目并生成评分标准</p>
            <p><strong>第四步：</strong>确认配置后保存，考试将进入"待阅卷"状态，可在阅卷中心开始阅卷</p>
          </div>
        }
        type="info"
        showIcon
        className="mb-6"
      />

      {/* 文件上传区域 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          {renderFileUploadArea('paper')}
        </Col>
        
        <Col xs={24} lg={12}>
          {renderFileUploadArea('answer')}
        </Col>
      </Row>

      {/* 配置状态总结 */}
      {(processingStatus.paper === 'completed' || processingStatus.answer === 'completed') && (
        <Card title="配置状态" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600 mb-2">
                {processingStatus.paper === 'completed' ? '✓' : '○'}
              </div>
              <div className="text-sm text-gray-600">试卷文件</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600 mb-2">
                {processingStatus.answer === 'completed' ? '✓' : '○'}
              </div>
              <div className="text-sm text-gray-600">参考答案</div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600 mb-2">
                {recognizedQuestions.length > 0 ? '✓' : '○'}
              </div>
              <div className="text-sm text-gray-600">AI识别配置</div>
            </div>
          </div>

          {processingStatus.paper === 'completed' && recognizedQuestions.length === 0 && (
            <div className="mt-4 text-center">
              <p className="text-gray-600 mb-3">文件已准备就绪，可以开始AI智能识别</p>
              <Button
                type="primary"
                size="large"
                icon={<RobotOutlined />}
                onClick={startAIRecognition}
              >
                开始AI智能识别
              </Button>
            </div>
          )}

          {recognizedQuestions.length > 0 && (
            <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <RobotOutlined className="text-green-600" />
                <span className="font-medium text-green-800">AI配置完成</span>
              </div>
              <p className="text-sm text-green-700 mb-3">
                已识别 {recognizedQuestions.length} 道题目，生成 {generatedRubrics.length} 个评分标准。
                配置已完成，可以保存并开始阅卷流程。
              </p>
              <Button
                type="primary"
                size="large"
                icon={<CheckCircleOutlined />}
                onClick={handleSaveConfiguration}
                className="w-full"
              >
                保存配置并开始阅卷
              </Button>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default ConfigureWorkspace;